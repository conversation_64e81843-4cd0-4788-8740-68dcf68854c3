<?php
include "../../config.php";

if (isset($_POST['reset'])) {
    $email = trim(filter_var(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['email'])))), FILTER_SANITIZE_STRIPPED));
    
    if (empty($email)) {
        $_SESSION['reset_msg'] = 'Email is Required';
        goto end;
    } else {
        $checkSql = mysqli_query($con, "SELECT * FROM users WHERE email = '$email' OR username = '$email'");
        if (mysqli_num_rows($checkSql) == 0) {
            $_SESSION['reset_msg'] = 'Email not found';
            goto end;
        } else {
            $userDet = mysqli_fetch_assoc($checkSql);
            $username = $userDet['username'];
            $email = $userDet['email'];
            $token = md5($current_date . $username . 'VTU');
            
            $resetLink = "https://" . $_SERVER['HTTP_HOST'] . "/reset?token=" . $token;
            
            $to = $email;
            $subject = "Password Reset - " . $config['site_name'];
            $message = "
                <html>
                <head>
                    <title>Password Reset</title>
                </head>
                <body>
                    <h2>Password Reset Request</h2>
                    <p>Hello " . $userDet['name'] . ",</p>
                    <p>You have requested to reset your password. Click the link below to reset it:</p>
                    <p><a href='" . $resetLink . "'>Reset Password</a></p>
                    <p>If you didn't request this, please ignore this email.</p>
                    <p>Best regards,<br>" . $config['site_name'] . "</p>
                </body>
                </html>
            ";
            
            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
            $headers .= "From: " . $config['site_name'] . " <" . $config['site_email'] . ">" . "\r\n";
            
            if (mail($to, $subject, $message, $headers)) {
                $_SESSION['reset_s_msg'] = "Password reset link has been sent to your email";
            } else {
                $_SESSION['reset_msg'] = "Failed to send reset link. Please try again.";
            }
        }
    }
}
end:
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="<?= $config['site_name'] ?> We offer modern solutions for internet connection, We are here to always serve you">
    <meta name="author" content="<?= $config['site_name'] ?>">
    <title>Forgot Password - <?= $config['site_name'] ?></title>
    <link rel="icon" type="image/png" href="../../favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <?php
    if (isset($_SESSION['reset_msg']) && !empty($_SESSION['reset_msg'])) {
    ?>
        <script>
            Swal.fire({
                title: "<?= $_SESSION["reset_msg"] ?>",
                icon: 'error',
                confirmButtonColor: '#4f46e5'
            }).then(() => {
                history.back()
            })
        </script>
    <?php
        $_SESSION['reset_msg'] = '';
    }
    if (isset($_SESSION['reset_s_msg']) && !empty($_SESSION['reset_s_msg'])) {
    ?>
        <script>
            Swal.fire({
                title: "<?= $_SESSION["reset_s_msg"] ?>",
                text: 'Please check your email for the reset link.',
                icon: 'success',
                confirmButtonColor: '#4f46e5'
            }).then(() => {
                window.location.replace("../login");
            })
        </script>
    <?php
        $_SESSION['reset_s_msg'] = '';
    }
    ?>

    <div class="container">
        <!-- Header -->
        <header>
            <a href="../login" class="logo">
                <i class="fas fa-bolt"></i>
                <span>sharesubdata.com.ng</span>
            </a>
            <nav class="desktop-nav">
                <a href="../login">Login</a>
                <a href="../sign_up">Sign Up</a>
            </nav>
            <button class="mobile-menu-btn" id="mobileMenuBtn">
                <i class="fas fa-bars"></i>
            </button>
        </header>
        
        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobileMenu">
            <nav>
                <a href="../login">Login</a>
                <a href="../sign_up">Sign Up</a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="page-header">
                <h1>Reset Password</h1>
                <p>Enter your Email to receive a reset link</p>
            </div>
            
            <div class="card">
                <form method="POST">
                    <!-- Email/Username Field -->
                    <div class="form-group">
                        <label for="email">Email</label>
                        <div class="input-wrapper">
                            <i class="fas fa-envelope icon"></i>
                            <input 
                                type="text" 
                                id="email" 
                                name="email" 
                                placeholder="Enter your Email" 
                                required
                            >
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" name="reset">
                        Send Reset Link
                    </button>

                    <!-- Login Link -->
                    <div class="form-footer">
                        <span>Remember your password?</span>
                        <a href="../login">Sign in</a>
                    </div>
                </form>
            </div>
            
            <!-- Footer -->
            <footer>
                &copy; <?= date('Y') ?> <?=$config['site_name'] ?>. All rights reserved.
            </footer>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
        });
    </script>
</body>
</html>

