<?php 
date_default_timezone_set('Africa/Lagos');

?>
      <!-- partial:partials/_footer.html -->
      <footer class="footer">
        <div class="d-sm-flex justify-content-center justify-content-sm-between">
          <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">© <script>document.write(new Date().getFullYear())</script> SHARE SUB. All rights reserved. </span>
          <span class="float-none float-sm-right d-block mt-1 mt-sm-0 text-center">Built with love <i class="ti-heart text-danger ms-1"></i></span>
        </div>
      </footer>
      <!-- partial -->
    </div>
    <!-- main-panel ends -->
  </div>
  <!-- page-body-wrapper ends -->
</div>
<!-- container-scroller -->

<!-- plugins:js -->
<script src="vendors/js/vendor.bundle.base.js"></script>
<!-- endinject -->
<!-- Plugin js for this page -->
<script src="vendors/chart.js/Chart.min.js"></script>
<script src="vendors/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="vendors/progressbar.js/progressbar.min.js"></script>

<!-- End plugin js for this page -->
<!-- inject:js -->
<script src="js/off-canvas.js"></script>
<script src="js/hoverable-collapse.js"></script>
<script src="js/template.js"></script>
<script src="js/settings.js"></script>
<script src="js/todolist.js"></script>
<!-- endinject -->
<!-- Custom js for this page-->
<script src="js/jquery.cookie.js" type="text/javascript"></script>
<script src="js/dashboard.js"></script>
<script src="js/Chart.roundedBarCharts.js"></script>

<!-- Custom sidebar fix script -->
<script src="js/sidebar-fix.js"></script>

<!-- Custom responsive scripts -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Set greeting based on time of day
    const hour = new Date().getHours();
    let greeting = '';
    
    if (hour < 12) {
      greeting = 'Good Morning';
    } else if (hour < 18) {
      greeting = 'Good Afternoon';
    } else {
      greeting = 'Good Evening';
    }
    
    const greetingEl = document.getElementById('greeting');
    if (greetingEl) {
      greetingEl.innerText = greeting;
    }
    
    // Create and append mobile sidebar backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'offcanvas-backdrop fade';
    backdrop.style.display = 'none';
    backdrop.style.zIndex = '999';
    document.body.appendChild(backdrop);
    
    // Fix mobile sidebar toggle
    const sidebarToggle = document.querySelector('.navbar-toggler[data-bs-toggle="offcanvas"]');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebarToggle && sidebar) {
      sidebarToggle.addEventListener('click', function(e) {
        e.preventDefault();
        sidebar.classList.toggle('active');
        
        if (sidebar.classList.contains('active')) {
          backdrop.classList.add('show');
          backdrop.style.display = 'block';
          document.body.style.overflow = 'hidden';
        } else {
          backdrop.classList.remove('show');
          setTimeout(function() {
            backdrop.style.display = 'none';
          }, 300);
          document.body.style.overflow = '';
        }
      });
      
      // Close sidebar when clicking backdrop
      backdrop.addEventListener('click', function() {
        sidebar.classList.remove('active');
        backdrop.classList.remove('show');
        setTimeout(function() {
          backdrop.style.display = 'none';
        }, 300);
        document.body.style.overflow = '';
      });
      
      // Close sidebar when clicking menu items on mobile
      const navLinks = sidebar.querySelectorAll('.nav-link:not([data-bs-toggle="collapse"])');
      navLinks.forEach(function(link) {
        link.addEventListener('click', function() {
          if (window.innerWidth < 992) {
            sidebar.classList.remove('active');
            backdrop.classList.remove('show');
            setTimeout(function() {
              backdrop.style.display = 'none';
            }, 300);
            document.body.style.overflow = '';
          }
        });
      });
    }
    
    // Make tables responsive on smaller screens
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
      if (!table.parentElement.classList.contains('table-responsive')) {
        const wrapper = document.createElement('div');
        wrapper.classList.add('table-responsive');
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
      }
    });
  });
</script>
<!-- End custom js for this page-->

<!-- Simple emergency fix for sidebar -->
<script>
  // Direct sidebar fix
  (function() {
    const sidebarToggle = document.querySelector('.navbar-toggler[data-bs-toggle="offcanvas"]');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebarToggle && sidebar) {
      sidebarToggle.removeAttribute('data-bs-toggle');
      sidebarToggle.removeAttribute('data-bs-target');
      
      sidebarToggle.addEventListener('click', function() {
        if (sidebar.classList.contains('active')) {
          sidebar.classList.remove('active');
          document.body.style.overflow = '';
        } else {
          sidebar.classList.add('active');
          document.body.style.overflow = 'hidden';
        }
      });
    }
  })();
</script>
</body>

</html>

