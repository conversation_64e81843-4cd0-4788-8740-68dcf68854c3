<?php
include "../../config.php";

if (isset($_GET['last'])) {
  $_SESSION['lastPage'] = $_GET['last'];
}
if (isset($_POST['login'])) {
  $username = strip_tags(mysqli_real_escape_string($con,trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['username']))) );
  $pass = strip_tags(trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['pass'])));
  if (empty($username)) {
    $_SESSION['log_err'] = "Username is Required";
  }elseif (empty($pass)) {
    $_SESSION['log_err'] = "Password is Required";
  }else{
    $quer = mysqli_query($con, "SELECT * FROM users WHERE email = '$username' OR phone = '$username'  OR username = '$username' ");
    if (mysqli_num_rows($quer) == 1) {
      $data = mysqli_fetch_assoc($quer);
      if (md5($pass) == $data['password']) {
        if ($data['suspended']== 'true') {
          $_SESSION['log_err'] = 'Account Suspended';
          goto endLogin;
        }
        if (empty($data['token']) || $data['token'] == null) {
          $new_token = hash('sha256', time());
          mysqli_query($con, "UPDATE users SET token = '$new_token' WHERE username = '$username'");
        }
        $_SESSION['data'] = $data;
        $_SESSION['username'] = $username;
        $_SESSION['token'] = "1e8789816530b40d8784c371d829db38";
        $_SESSION['LAST_ACTIVITY'] = time();
        $lastPage = '';
        if (isset($_SESSION['lastPage'])) {
          $lastPage = $_SESSION['lastPage'];
          ?>
          <script type="text/javascript">
            window.location.replace('<?=$lastPage?>')
          </script>
          <?php
        }else{
          header("location:../dash");
        }
      }else{
        $_SESSION['log_err'] = "Wrong Password";
      }
    }else{
      $_SESSION['log_err'] = "Account Not Found";
    }
  }
}
endLogin:
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="<?=$config['site_name'] ?> We offer modern solutions for internet connection, We are here to always serve you">
  <meta name="author" content="<?=$config['site_name'] ?>">
  <title>Login - <?=$config['site_name'] ?></title>
  <link rel="icon" type="image/png" href="../../favicon.png">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../assets/css/styles.css">
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
  <?php 
  if (isset($_SESSION['reg_s_msg']) && !empty($_SESSION['reg_s_msg'])) {
  ?>
  <script>
    Swal.fire({
      title: "<?= $_SESSION["reg_s_msg"] ?>",
      text: 'Please check your email to verify your email address.',
      icon: 'success',
      confirmButtonColor: '#4f46e5'
    }).then(()=>{
      window.location.replace("../login");
    })
  </script>
  <?php
    $_SESSION['reg_s_msg'] = '';
  }
  if (isset($_SESSION['log_err']) && !empty($_SESSION['log_err'])) {
  ?>
  <script>
    Swal.fire({
      title: "<?= $_SESSION["log_err"] ?>",
      icon: 'error',
      confirmButtonColor: '#4f46e5'
    })
  </script>
  <?php
    $_SESSION['log_err'] = '';
  }
  ?>

  <div class="container">
    <!-- Header -->
    <header>
      <div class="brand-logo">
        <a href="https://sharesubdata.com.ng/" class="logo">
          <img src="../../assets/img/logo.png" alt="SHARE SUB Logo" style="height: 40px; max-width: 200px;">
        </a>
      </div>
      <nav class="desktop-nav">
        <a href="../login">Login</a>
        <a href="../sign_up">Sign Up</a>
      </nav>
      <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
      </button>
    </header>
    
    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
      <nav>
        <a href="../login">Login</a>
        <a href="../sign_up">Sign Up</a>
      </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
      <div class="page-header">
        <h1>Welcome Back</h1>
        <p>Sign in to your account to continue</p>
      </div>
      
      <div class="card">
        <form method="POST">
          <!-- Username Field -->
          <div class="form-group">
            <label for="username">Email, Phone or Username</label>
            <div class="input-wrapper">
              <i class="fas fa-user icon"></i>
              <input 
                type="text" 
                id="username" 
                name="username" 
                placeholder="Enter your credentials" 
                required
              >
            </div>
          </div>

          <!-- Password Field -->
          <div class="form-group">
            <div class="label-row">
              <label for="password">Password</label>
              <a href="../forgot">Forgot password?</a>
            </div>
            <div class="input-wrapper">
              <i class="fas fa-lock icon"></i>
              <input 
                type="password" 
                id="password" 
                name="pass" 
                placeholder="Enter your password" 
                required
              >
              <button 
                type="button" 
                id="togglePassword" 
                class="toggle-password"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <!-- Remember Me -->
          <div class="checkbox-group">
            <input 
              type="checkbox" 
              id="remember" 
            >
            <label for="remember">
              Remember me
            </label>
          </div>

          <!-- Submit Button -->
          <button type="submit" name="login">
            Sign In
          </button>

          <!-- Sign Up Link -->
          <div class="form-footer">
            <span>Don't have an account?</span>
            <a href="../sign_up">Sign up</a>
          </div>
        </form>
      </div>
      
      <!-- Footer -->
      <footer>
        &copy; <?= date('Y') ?> <?=$config['site_name'] ?>. All rights reserved.
      </footer>
    </div>
  </div>

  <script>
    // Toggle password visibility
    const togglePassword = document.querySelector('#togglePassword');
    const password = document.querySelector('#password');

    togglePassword.addEventListener('click', function() {
      const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
      password.setAttribute('type', type);
      this.querySelector('i').classList.toggle('fa-eye');
      this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');

    mobileMenuBtn.addEventListener('click', function() {
      mobileMenu.classList.toggle('active');
    });
  </script>
</body>
</html>

