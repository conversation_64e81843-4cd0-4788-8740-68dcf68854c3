/* Responsive CSS for SHARE SUB Admin Dashboard */

/* Mobile & Tablet Styles */
@media (max-width: 991px) {
  /* Navbar & Header */
  .navbar .navbar-brand-wrapper {
    width: 55px !important;
  }
  
  .navbar .navbar-menu-wrapper {
    width: calc(100% - 55px) !important;
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
  
  .welcome-text {
    font-size: 1.2rem !important;
  }
  
  .welcome-sub-text {
    font-size: 0.9rem !important;
    margin-bottom: 0;
  }
  
  /* Sidebar */
  .sidebar {
    z-index: 1000 !important;
    position: fixed !important;
    top: 70px !important;
    left: -300px !important;
    height: calc(100vh - 70px) !important;
    width: 260px !important;
    background-color: #fff !important;
    box-shadow: none !important;
    transition: left 0.3s ease !important;
    overflow-y: auto !important;
    padding-top: 10px !important;
    -webkit-overflow-scrolling: touch;
  }
  
  .sidebar.active {
    left: 0 !important;
    box-shadow: 2px 0 10px rgba(0,0,0,0.2) !important;
  }
  
  body.sidebar-open {
    overflow: hidden;
  }
  
  .sidebar .nav {
    padding-left: 0;
    padding-right: 0;
  }
  
  .sidebar .nav .nav-item {
    padding: 0;
  }
  
  .sidebar .nav .nav-item .menu-title {
    font-size: 0.9rem;
  }
  
  /* Dashboard Content */
  .content-wrapper {
    padding: 1.5rem 1rem !important;
  }
  
  .statistics-details {
    flex-direction: column !important;
    gap: 15px;
  }
  
  .card-statistics {
    width: 100% !important;
  }
  
  .btn-wrapper {
    display: none;
  }
  
  .grid-margin {
    margin-bottom: 1rem !important;
  }
  
  .card {
    margin-bottom: 15px !important;
  }
  
  .card-body {
    padding: 1rem !important;
  }
  
  .card-title {
    font-size: 1rem !important;
    margin-bottom: 0.8rem !important;
  }
  
  .rate-percentage {
    font-size: 1.2rem !important;
  }
  
  /* Tables */
  .table-responsive {
    margin-bottom: 15px;
  }
  
  table {
    min-width: 650px;
  }
  
  th, td {
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
  }
  
  /* Forms */
  .form-group {
    margin-bottom: 1rem !important;
  }
  
  /* Chart containers */
  canvas {
    max-height: 250px !important;
  }
  
  /* Fix for sidebar backdrop */
  .offcanvas-backdrop {
    z-index: 999 !important;
    opacity: 0.5 !important;
  }
  
  .show.offcanvas-backdrop {
    display: block !important;
  }
}

/* Small Mobile Styles */
@media (max-width: 576px) {
  .statistics-details {
    gap: 10px;
  }
  
  .card-body {
    padding: 0.75rem !important;
  }
  
  .navbar-brand img {
    max-width: 80px;
  }
  
  .card-title {
    font-size: 0.9rem !important;
  }
  
  .rate-percentage {
    font-size: 1rem !important;
  }
  
  .badge {
    font-size: 0.7rem !important;
    padding: 0.2rem 0.4rem !important;
  }
  
  .footer {
    padding: 1rem !important;
    font-size: 0.8rem !important;
  }
  
  .container-scroller {
    overflow-x: hidden;
  }
}

/* Fix for table overflow */
.table-responsive {
  overflow-x: auto;
}

/* Fix for sidebar submenus */
.sidebar .nav.sub-menu {
  padding-left: 1rem !important;
}

/* Better spacing for mobile forms */
@media (max-width: 767px) {
  .form-control {
    font-size: 0.9rem !important;
    padding: 0.5rem 0.75rem !important;
    height: auto !important;
  }
  
  label {
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
  }
  
  .btn {
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
  }
}

/* Fix for charts on mobile */
@media (max-width: 576px) {
  canvas#sts {
    height: 200px !important;
  }
}

/* Fix for card-statistics */
@media (max-width: 991px) {
  .statistics-details .card-statistics {
    min-height: auto !important;
  }
}

/* Bootstrap 5 offcanvas override */
@media (max-width: 991.98px) {
  .offcanvas-start {
    top: 70px !important;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    width: 260px !important;
  }
  
  .offcanvas {
    visibility: visible !important;
  }
} 