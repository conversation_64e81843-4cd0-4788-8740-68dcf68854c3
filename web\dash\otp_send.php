<?php
include '../../config.php';
include '../../conn.php';

// Check if OTP verification is being requested
if (isset($_POST['otp'])) {
    $otp = mysqli_real_escape_string($con, $_POST['otp']);
    $username = mysqli_real_escape_string($con, $_POST['username'] ?? '');
    
    // Fetch user data securely
    $userQuery = mysqli_query($con, "SELECT otp FROM users WHERE username = '$username'");
    if(!$userQuery || mysqli_num_rows($userQuery) == 0) {
        die('404'); // User not found
    }
    
    $userData = mysqli_fetch_assoc($userQuery);
    $otps = json_decode($userData['otp']);
    
    if(json_last_error() !== JSON_ERROR_NONE) {
        die('500'); // Invalid OTP data format
    }
    
    $t = time();
    if ($otp == $otps->otp) {
        if (($t - $otps->time) > 300) {
            die('304'); // OTP expired
        } else {
            die('200'); // Success
        }
    } else {
        die('403'); // Invalid OTP
    }
} else {
    // OTP Sending Logic
    $adm = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM adm LIMIT 1"));
    
    require_once '../mailer/vendor/autoload.php';
    $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        // Generate OTP and timestamp
        $otp = mt_rand(100000, 999999);
        $username = mysqli_real_escape_string($con, $data['username'] ?? '');
        $t = time();
        $otp_tm = json_encode(array('time' => $t, 'otp' => $otp));
        
        // Update user record with new OTP
        mysqli_query($con, "UPDATE users SET otp = '".mysqli_real_escape_string($con, $otp_tm)."' WHERE username = '$username'");
        
        // SMTP Configuration - USING YOUR SPECIFIC CREDENTIALS
        $mail->SMTPDebug = 2; // Enable for troubleshooting (set to 0 in production)
        $mail->isSMTP(); 
        $mail->Host       = 'server305.web-hosting.com';  
        $mail->SMTPAuth   = true; 
        $mail->Username   = '<EMAIL>'; // Your email address
        $mail->Password   = '7rDmfVZ~&Nda';     // Your email password
        $mail->SMTPSecure = 'ssl';              // Using SSL encryption
        $mail->Port       = 465;                // Standard port for SSL
        $mail->setFrom('<EMAIL>', $config['site_name']);
        $mail->addAddress($data['email'], $data['name']);
        $mail->addReplyTo('<EMAIL>', 'Information');
        $mail->isHTML(true);                                  
        
        // Email Content
        $mail->Subject = 'PASSWORD RESET';
        $siteName = $config['site_name'];
        $siteLink = $config['site_link'];
        $siteAdd = $config['address'];
        
        $mail->Body = "<div style='font-family: Helvetica,Arial,sans-serif;min-width:1000px;overflow:auto;line-height:2'>
            <div style='margin:50px auto;width:70%;padding:20px 0'>
                <div style='border-bottom:1px solid #eee'>
                    <a href='' style='font-size:1.4em;color: #00466a;text-decoration:none;font-weight:600'>$siteName</a>
                </div>
                <p style='font-size:1.1em'>Hi,</p>
                <p>Use the following OTP to complete your PIN resetting procedures. OTP is valid for 5 minutes</p>
                <h2 style='background: #00466a;margin: 0 auto;width: max-content;padding: 0 10px;color: #fff;border-radius: 4px;'>$otp</h2>
                <p style='font-size:0.9em;'>Regards,<br />$siteName</p>
                <hr style='border:none;border-top:1px solid #eee' />
                <div style='float:right;padding:8px 0;color:#aaa;font-size:0.8em;line-height:1;font-weight:300'>
                    <p>$siteName Inc</p>
                    <p>$siteAdd</p>
                </div>
            </div>
        </div>";
        
        if($mail->send()) {
            die('200'); // Success
        } else {
            die('500'); // Mail sending failed
        }
    } catch (Exception $e) {
        die('Mailer Error: ' . $mail->ErrorInfo);
    }
}
?>