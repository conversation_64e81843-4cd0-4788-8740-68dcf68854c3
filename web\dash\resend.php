<?php
include "../../config.php";
$adm = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM adm"));
require_once '../mailer/vendor/autoload.php';

$mail = new \PHPMailer\PHPMailer\PHPMailer(true);

try {
    //Server settings
    $mail->SMTPDebug = 2; // Enable verbose debug output
    $mail->isSMTP();
    $mail->Host       = 'server305.web-hosting.com';
    $mail->SMTPAuth   = true;
    $mail->Username   = '<EMAIL>'; // Your actual email
    $mail->Password   = '7rDmfVZ~&Nda';      // Your actual password
    $mail->SMTPSecure = 'ssl';
    $mail->Port       = 465;

    //Recipients
    $mail->setFrom('<EMAIL>', $config['site_name']);
    $mail->addAddress($data['email'], $data['name']);
    $mail->addReplyTo($config['email'], 'Information');

    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Email Verification';
    
    $ciphering = "AES-128-CTR";
    $options = 0;
    $encryption_iv = '****************';
    $encryption_key = "THISISTHEENCRYPTKEY";
    $enc_str = openssl_encrypt($data['username'], $ciphering, $encryption_key, $options, $encryption_iv);
    $encUrl = urlencode($enc_str);
    
    $mail->Body = "Thank you for registering at {$config['site_name']}. <br> Your account has been registered successfully! kindly click on the link below to verify your email.  <br> <b> If you are unable to click on the link, just copy it and paste it in browser. <b> <br> Thanks.<br><br><br> <p style='color:blue'>https://{$config['site_link']}/verify?q={$encUrl} </p>";

    $mail->send();
    echo '<script>alert("Verification Link Sent Successfully, Please check your SPAM folder if not in inbox!"); window.location.href = "index";</script>';
} catch (Exception $e) {
    echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
}

include 'footer.php';
?>