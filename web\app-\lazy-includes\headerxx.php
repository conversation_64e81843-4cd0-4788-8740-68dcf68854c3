<?php 
// error_reporting(0);
include "../../config.php";

if (!isset($_SESSION['username'])) {
  header('location:logout');
}
if ($_SESSION['token'] !== "1e8789816530b40d8784c371d829db38") {
  header('location:login.php');
}
if (!isset($_SESSION['LAST_ACTIVITY'])) {
 header('location:login.php');
}
if ( time() - $_SESSION['LAST_ACTIVITY'] > 300000) {
  $last = $_SERVER['REQUEST_URI'];
  header("location:?last={$last}");
}
$_SESSION['LAST_ACTIVITY'] = time();
function parseAmt($amt){
  $val = intval($amt);
  if ($val > 999999){
    return substr_replace(substr_replace($val, ',', -3, 0), ',', -7, 0);
  }elseif($val > 999){
    return substr_replace($val, ',', -3, 0);
  }else{
    return $val;
  }
}

if (($data['reserveBank'] == null || empty($data['reserveBank']) || $data['reserveBank'] == '[]') && !empty(json_decode($config['monnify'])[0])) {
  $username = $data['username'];
  $email = $data['email'];
  $name = $data['name'];
  try {
    $apik = trim(json_decode($config['monnify'])[0]);
    $seck = trim(json_decode($config['monnify'])[2]);
    $apiCon = trim(json_decode($config['monnify'])[1]);
    $au = base64_encode("{$apik}:{$seck}");
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v1/auth/login/");
    // curl_setopt($ch, CURLOPT_URL, "https://sandbox.monnify.com/api/v1/auth/login/");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);

    curl_setopt($ch, CURLOPT_POST, TRUE);

    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Basic ".$au
    ));

    $response = curl_exec($ch);
    curl_close($ch);
    $tk = json_decode($response)->responseBody->accessToken;

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v1/bank-transfer/reserved-accounts");
    // curl_setopt($ch, CURLOPT_URL, "https://sandbox.monnify.com/api/v1/bank-transfer/reserved-accounts");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);

    curl_setopt($ch, CURLOPT_POST, TRUE);
    $tRef = md5($name.$email.time());
    curl_setopt($ch, CURLOPT_POSTFIELDS, "{
      \"accountName\": \"{$name}\",
      \"accountReference\": \"{$tRef}\",
      \"currencyCode\": \"NGN\",
      \"contractCode\": \"{$apiCon}\",
      \"customerName\": \"{$name}\",
      \"customerEmail\": \"{$email}\",
      \"getAllAvailableBanks\": true
    }");

    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$tk
    ));

    $response = curl_exec($ch);
    curl_close($ch);
    mysqli_query($con, "INSERT INTO error (det) VALUES ('$response')");


// $js = '{"requestSuccessful":true,"responseMessage":"success","responseCode":"0","responseBody":{"contractCode":"**********","accountReference":"jsnow1234","accountName":"Tai","currencyCode":"NGN","customerEmail":"<EMAIL>","customerName":"John Snow Limited","accountNumber":"**********","bankName":"Wema bank","bankCode":"035","collectionChannel":"RESERVED_ACCOUNT","reservationReference":"T0SYHTEHG3F9BUH37K59","reservedAccountType":"GENERAL","status":"ACTIVE","createdOn":"2022-05-14 08:10:57.765","incomeSplitConfig":[],"restrictPaymentSource":false}}';
    $accDet = [];
    $res = json_decode($response);
    $curl = curl_init();

    curl_setopt_array($curl, array(
      CURLOPT_URL => 'https://api.monnify.com/api/v1/bank-transfer/reserved-accounts/add-linked-accounts/'.$res->responseBody->accountReference,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'PUT',
      CURLOPT_POSTFIELDS =>'{
        "getAllAvailableBanks": false,
        "preferredBanks": ["50515", "232"]
      }',
      CURLOPT_HTTPHEADER => array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$tk
      ),
    ));
// '{
//        "getAllAvailableBanks": false,
//        "preferredBanks": ["50515", "232"]
//      }',
    $new_response = curl_exec($curl);

    curl_close($curl);
    mysqli_query($con, "INSERT INTO error (det) VALUES ('$new_response')");
    $res = json_decode($new_response);
    if ($res->requestSuccessful && $res->responseMessage == 'success') {

      $accDet = $res->responseBody->accounts;

    }
    $accountReference = $res->responseBody->accountReference;
    $genAccDet = json_encode($accDet);
    mysqli_query($con, "UPDATE users SET reserveBank = '$genAccDet' WHERE username = '$username'");

  } catch (Exception $e) {
    $myfile = fopen("errorFile.txt", "a") or die("Unable to open file!");
    fwrite($myfile, '\r\n '.$current_date.': '.$e.'\r\n');
    fclose($myfile);
  }
}



?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="generator" content="">
    <title><?=$config['site_name'] ?></title>


    <!-- Favicons -->
    <link rel="apple-touch-icon" href="assets/img/favicon180.png" sizes="180x180">
    <link rel="icon" href="assets/img/favicon32.png" sizes="32x32" type="image/png">
    <link rel="icon" href="assets/img/favicon16.png" sizes="16x16" type="image/png">

    <!-- Google fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- bootstrap icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">

    <!-- swiper carousel css -->
    <link rel="stylesheet" href="assets/vendor/swiperjs-6.6.2/swiper-bundle.min.css">
    
        <!-- nouislider CSS -->
    <link href="assets/vendor/nouislider/nouislider.min.css" rel="stylesheet">
<link rel="stylesheet" href="lazy-dev/assets/css/bootstrap.min.css">
    <!-- date rage picker -->
    <link rel="stylesheet" href="assets/vendor/daterangepicker/daterangepicker.css">
<script src="//ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
		
<link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>




    <!-- style css for this template -->
    <link href="assets/css/style.css" rel="stylesheet" id="style">
</head>

<body class="body-scroll" data-page="wallet" onload="greet(); alertinfo()">

    <!-- loader section -->
    <div class="container-fluid loader-wrap">
        <div class="row h-100">
            <div class="col-10 col-md-6 col-lg-5 col-xl-3 mx-auto text-center align-self-center">
                <div class="logo-wallet">
                    <div class="wallet-bottom">
                    </div>
                    <div class="wallet-cards"></div>
                    <div class="wallet-top">
                    </div>
                </div>
                <p class="mt-4"><span class="text-secondary">Loading <?=$config['site_name'] ?></span><br><strong>Please
                        Wait...</strong></p>
            </div>
        </div>
    </div>
    <!-- loader section ends -->

    <!-- Sidebar main menu -->
    <div class="sidebar-wrap  sidebar-overlay">
        <!-- Add pushcontent or fullmenu instead overlay -->
        <div class="closemenu text-muted">Close Menu</div>
        <div class="sidebar ">
            <!-- user information -->
            <div class="row my-3">
                <div class="col-12 profile-sidebar">
                    <div class="clearfix"></div>
                    <div class="circle small one"></div>
                    <div class="circle small two"></div>
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                        viewBox="0 0 194.287 141.794" class="menubg">
                        <defs>
                            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1"
                                gradientUnits="objectBoundingBox">
                                <stop offset="0" stop-color="#09b2fd" />
                                <stop offset="1" stop-color="#6b00e5" />
                            </linearGradient>
                        </defs>
                        <path id="menubg"
                            d="M672.935,207.064c-19.639,1.079-25.462-3.121-41.258,10.881s-24.433,41.037-49.5,34.15-14.406-16.743-50.307-29.667-32.464-19.812-16.308-41.711S500.472,130.777,531.872,117s63.631,21.369,93.913,15.363,37.084-25.959,56.686-19.794,4.27,32.859,6.213,44.729,9.5,16.186,9.5,26.113S692.573,205.985,672.935,207.064Z"
                            transform="translate(-503.892 -111.404)" fill="url(#linear-gradient)" />
                    </svg>

                    <div class="row mt-3">
                        <div class="col-auto">
                            <figure class="avatar avatar-80 rounded-20 p-1 bg-white shadow-sm">
                                <img src="assets/img/user1.jpg" alt="" class="rounded-18">
                            </figure>
                        </div>
                        <div class="col px-0 align-self-center">
                            <h5 class="mb-2"><?=explode(' ', $data['name'])[1] ?></h5>
                            <p class="text-muted size-12">Balance: ₦<?=parseAmt($data['bal'])?></p>
   <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="darkmodeswitch">
                                <label class="form-check-label text-muted px-2 " for="darkmodeswitch">Dark Mode</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- user emnu navigation -->
            <div class="row">
                <div class="col-12">
                    <ul class="nav nav-pills">
                        <li class="nav-item">
                            <a class="nav-link active" aria-current="page" href="welcome.php">
                                <div class="avatar avatar-40 icon"><i class="bi bi-house-door"></i></div>
                                <div class="col">Dashboard</div>
                                <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="data.php" tabindex="-1">
                                <div class="avatar avatar-40 icon"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-router" viewBox="0 0 16 16">
  <path d="M5.525 3.025a3.5 3.5 0 0 1 4.95 0 .5.5 0 1 0 .707-.707 4.5 4.5 0 0 0-6.364 0 .5.5 0 0 0 .707.707Z"/>
  <path d="M6.94 4.44a1.5 1.5 0 0 1 2.12 0 .5.5 0 0 0 .708-.708 2.5 2.5 0 0 0-3.536 0 .5.5 0 0 0 .707.707ZM2.5 11a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1Zm4.5-.5a.5.5 0 1 0 1 0 .5.5 0 0 0-1 0Zm2.5.5a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1Zm1.5-.5a.5.5 0 1 0 1 0 .5.5 0 0 0-1 0Zm2 0a.5.5 0 1 0 1 0 .5.5 0 0 0-1 0Z"/>
  <path d="M2.974 2.342a.5.5 0 1 0-.948.316L3.806 8H1.5A1.5 1.5 0 0 0 0 9.5v2A1.5 1.5 0 0 0 1.5 13H2a.5.5 0 0 0 .5.5h2A.5.5 0 0 0 5 13h6a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5h.5a1.5 1.5 0 0 0 1.5-1.5v-2A1.5 1.5 0 0 0 14.5 8h-2.306l1.78-5.342a.5.5 0 1 0-.948-.316L11.14 8H4.86L2.974 2.342ZM14.5 9a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 1 .5-.5h13Z"/>
  <path d="M8.5 5.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"/>
</svg></div>
                                <div class="col">Buy Data</div>
                                <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                            </a>
                        </li>



                        <li class="nav-item">
                            <a class="nav-link" href="airtime.php" tabindex="-1">
                                <div class="avatar avatar-40 icon"><i class="bi bi-telephone-inbound"></i></div>
                                <div class="col">Buy Airtime</div>
                                <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                            </a>
                        </li>






                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button"
                                aria-expanded="false">
                                <div class="avatar avatar-40 icon"><i class="bi bi-cart"></i></div>
                                <div class="col">Fund Wallet</div>
                                <div class="arrow"><i class="bi bi-chevron-down plus"></i> <i
                                        class="bi bi-chevron-up minus"></i>
                                </div>
                            </a>
                            <ul class="dropdown-menu">
                                
                                <li>
                                    <a class="dropdown-item nav-link" href="fund.php">
                                        <div class="avatar avatar-40 icon"><i class="bi bi-cash-stack"></i>
                                        </div>
                                        <div class="col align-self-center">Automated Bank</div>
                                        <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                                    </a>
                                </li>

                                
                                <li>
                                    <a class="dropdown-item nav-link" href="card.php">
                                        <div class="avatar avatar-40 icon"><i class="bi bi-credit-card-2-back"></i>
                                        </div>
                                        <div class="col align-self-center">Card Payment</div>
                                        <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        



                        <li class="nav-item">
                            <a class="nav-link" href="transactions.php" tabindex="-1">
                                <div class="avatar avatar-40 icon"><i class="bi bi-printer"></i></div>
                                <div class="col">Transactions </div>
                                <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="profile.php" tabindex="-1">
                                <div class="avatar avatar-40 icon"><i class="bi bi-person-circle"></i></div>
                                <div class="col">My Profile</div>
                                <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                            </a>
                        </li>
                        <li class="nav-item" onclick="Swal.fire('You want to log out?','','question').then((res)=>{if(res.isConfirmed){
            window.location.replace('logout');
          }})">
                            <a class="nav-link" href="logout.php" tabindex="-1">
                                <div class="avatar avatar-40 icon"><i class="bi bi-box-arrow-right"></i></div>
                                <div class="col">Logout</div>
                                <div class="arrow"><i class="bi bi-chevron-right"></i></div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- Sidebar main menu ends -->

    <!-- Begin page -->
    <main class="h-100">

        <!-- Header -->
        <header class="header position-fixed">
            <div class="row">
                <div class="col-auto">
                    <a href="javascript:void(0)" target="_self" class="btn btn-light btn-44 menu-btn">
                        <i class="bi bi-list"></i>
                    </a>
                </div>

                <div class="col text-center">
                    <div class="logo-small">
                        <h6 class="mt-1 mb-0"><?=$config['site_name'] ?></h6>
                    </div>
                </div>
                <div class="col-auto">
                    <a href="profile.php" target="_self" class="btn btn-light btn-44">
                        <i class="bi bi-person-circle"></i>
                        <span class="count-indicator"></span>
                    </a>
                </div>
            </div>
        </header>
        <!-- Header ends -->
