# Place in /c:/laragon/www/sharesubnew/.htaccess

Options -Indexes
DirectoryIndex index.php index.html

# Ensure PHP files are processed correctly
<Files "*.php">
    SetHandler application/x-httpd-php
</Files>

RewriteEngine on

# --- Temporarily Commented Out for Debugging ---
# Internally add .php for extensionless requests if the file exists
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [L]
# 
# # HTTPS/WWW rules (uncomment and adjust if needed for your live domain)
# # <IfModule mod_rewrite.c>
# #	RewriteCond %{HTTPS} off
# #	RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# #	
# #	RewriteCond %{HTTP_HOST} !^www\.yourdomain\.com [NC] # Replace yourdomain.com
# #	RewriteRule (.*) https://www.yourdomain.com%{REQUEST_URI} [L,R=301] # Replace yourdomain.com
# # </IfModule>
# 
# # ModSecurity settings (copied from /web/.htaccess)
# <IfModule mod_security.c>
# SecFilterEngine Off
# SecFilterScanPOST Off
# </IfModule>
# 
# # Custom Error Documents (copied from /web/.htaccess)
# # ErrorDocument 400 400
# # ErrorDocument 401 401
# # ErrorDocument 403 403
# # ErrorDocument 404 <h1>Page-Not-Found</h1> # Or point to a custom /404.php page
# # ErrorDocument 503 503
# 
# # Default Access Control (copied from /web/.htaccess)
# <RequireAll>
#     Require all granted
# </RequireAll>
# --- End Temporarily Commented Out ---

# php -- BEGIN cPanel-generated handler, do not edit (COMMENTED OUT FOR LARAGON)
# Set the “ea-php81” package as the default “PHP” programming language.
# <IfModule mime_module>
#   AddHandler application/x-httpd-ea-php81 .php .php8 .phtml
# </IfModule>
# php -- END cPanel-generated handler, do not edit (COMMENTED OUT FOR LARAGON)
