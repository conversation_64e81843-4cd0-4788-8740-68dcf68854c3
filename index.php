<?php
// This ensures the file is processed as PHP
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- SEO Meta Tags -->
  <title>SHARE SUB - Instant Digital Recharge Solutions | Fast & Secure VTU</title>
  <meta name="description" content="SHARE SUB provides instant and secure digital recharge solutions, including airtime, data, and bill payments. Enjoy fast transactions and a seamless experience.">
  <meta name="keywords" content="VTU, SHARE SUB, Airtime, Data, Bill Payment, Fast Recharge, Digital Payment">
  <meta name="author" content="SHARE SUB">
  <meta name="robots" content="index, follow">

  <!-- Open Graph for Social Media -->
  <meta property="og:title" content="SHARE SUB - Affordable Data, Airtime, Bill Payments">
  <meta property="og:description" content="Get the best deals on data bundles, airtime top-ups, electricity bills, and cable TV subscriptions with SHARE SUB.">
  <meta property="og:image" content="https://sharesubdata.com.ng/android-chrome-512x512.png">
  <meta property="og:url" content="https://sharesubdata.com.ng/">
  <meta property="og:type" content="website">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="SHARE SUB - Affordable Data, Airtime, Bill Payments">
  <meta name="twitter:description" content="Get the best deals on data bundles, airtime top-ups, electricity bills, and cable TV subscriptions with SHARE SUB.">
  <meta name="twitter:image" content="https://sharesubdata.com.ng/android-chrome-512x512.png">

  <!-- Favicon -->
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="./favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">
  <meta name="google-site-verification" content="_HCe9WIrkoNMCZBU90f3C3svq3kPftWvE9cGzefojQo" />
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Lucide Icons -->
  <script src="https://unpkg.com/lucide@latest"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    body {
      font-family: 'Inter', sans-serif;
    }
    .max-h-0 {
      max-height: 0;
    }
    .max-h-96 {
      max-height: 24rem;
    }
    /* Glass effect for header */
    .glass-effect {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }
    /* Custom scrollbar (optional) */
    ::-webkit-scrollbar {
      width: 6px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #002496; /* Changed to blue */
      border-radius: 3px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #001a70; /* Darker blue on hover */
    }
    html {
      scroll-behavior: smooth; /* Enable smooth scrolling */
    }
  </style>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'primary-blue': '#002496',
            'primary-green': '#00a036',
            'dark-blue': '#001a70',
            'light-blue': '#e6edff',
            'light-green': '#e6ffe9',
            blue: {
              50: '#e6edff',
              100: '#ccdaff',
              400: '#002496',
              600: '#001a70',
              700: '#001557',
            },
            green: {
              50: '#e6ffe9',
              100: '#ccffd6',
              400: '#00a036',
              600: '#00802b',
              700: '#006622',
            }
          }
        }
      }
    }
  </script>
</head>
<body class="min-h-screen bg-gray-50 text-gray-800">
  <!-- Header -->
  <header id="header" class="fixed top-0 left-0 right-0 z-50 transition-colors duration-300 bg-transparent py-4">
    <div class="container mx-auto px-4 md:px-6 flex justify-between items-center">
      <div class="flex items-center">
        <a href="#" class="block">
          <img src="./assets/img/logosharesub.png" alt="SHARE SUB Logo" class="h-10"> <!-- Adjust height as needed -->
        </a>
      </div>
      <!-- Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-6 lg:space-x-8">
        <a href="#services" class="text-gray-700 hover:text-primary-blue font-medium transition-colors duration-200">
          Services
        </a>
        <a href="#features" class="text-gray-700 hover:text-primary-blue font-medium transition-colors duration-200">
          Features
        </a>
        <a href="#how-it-works" class="text-gray-700 hover:text-primary-blue font-medium transition-colors duration-200">
          How it Works
        </a>
        <a href="#testimonials" class="text-gray-700 hover:text-primary-blue font-medium transition-colors duration-200">
          Testimonials
        </a>
        <a href="#faq" class="text-gray-700 hover:text-primary-blue font-medium transition-colors duration-200">
          FAQ
        </a>
        <a href="#download-app" class="text-gray-700 hover:text-primary-blue font-medium transition-colors duration-200">
          Download App
        </a>
        <a href="/web/login/" class="bg-primary-blue hover:bg-opacity-90 text-white px-5 py-2 rounded-full font-medium transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
          Sign In
        </a>
      </nav>
      <!-- Mobile Menu Button -->
      <button id="mobile-menu-button" class="md:hidden text-primary-blue">
        <i data-lucide="menu" class="block" width="24" height="24"></i>
        <i data-lucide="x" class="hidden" width="24" height="24"></i>
      </button>
    </div>
    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden glass-effect absolute top-full left-0 w-full shadow-lg py-4 px-6 flex flex-col space-y-3 hidden">
      <a href="#services" class="text-gray-800 hover:text-primary-blue font-medium py-2">
        Services
      </a>
      <a href="#features" class="text-gray-800 hover:text-primary-blue font-medium py-2">
        Features
      </a>
      <a href="#how-it-works" class="text-gray-800 hover:text-primary-blue font-medium py-2">
        How it Works
      </a>
      <a href="#testimonials" class="text-gray-800 hover:text-primary-blue font-medium py-2">
        Testimonials
      </a>
      <a href="#faq" class="text-gray-800 hover:text-primary-blue font-medium py-2">
        FAQ
      </a>
      <a href="#download-app" class="text-gray-800 hover:text-primary-blue font-medium py-2 block transition-colors duration-200">
        Download App
      </a>
       <a href="/web/login/" class="bg-primary-blue hover:bg-opacity-90 text-white px-5 py-2 rounded-full font-medium transition-all duration-200 mt-2 text-center block shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
        Sign In
      </a>
    </div>
  </header>

  <main>
    <!-- Hero Section -->
    <section class="relative pt-32 pb-20 md:pt-40 md:pb-28 bg-gradient-to-b from-white to-light-blue">
      <div class="container mx-auto px-4 md:px-6 relative z-10">
        <div class="flex flex-col md:flex-row items-center gap-8">
          <div class="w-full md:w-1/2 text-center md:text-left">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-gray-900">
              <span class="block">Instant Digital</span>
              <span class="text-primary-blue">
                Recharge Solutions
              </span>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 mb-8 max-w-lg mx-auto md:mx-0">
              Top up your airtime and data, pay electricity bills, subscribe to cable TV,
              and utility bills with Share Sub's fast and secure platform.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
              <a href="/web/login/" class="px-8 py-3 bg-primary-blue hover:bg-opacity-90 text-white font-medium rounded-full transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-1 flex items-center justify-center">
                Get Started
                <i data-lucide="arrow-right" class="ml-2" width="18" height="18"></i>
              </a>
              <a href="#services" class="px-8 py-3 border border-primary-blue text-primary-blue hover:bg-light-blue font-medium rounded-full transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-1 flex items-center justify-center">
                Learn More
                <i data-lucide="arrow-down" class="ml-2" width="18" height="18"></i>
              </a>
            </div>
            <div class="mt-10 flex items-center gap-4 justify-center md:justify-start">
              <div class="flex -space-x-2">
                 <!-- Keep original images -->
                <img src="https://images.unsplash.com/photo-1522529599102-193c0d76b5b6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="User" class="w-10 h-10 rounded-full border-2 border-white object-cover shadow-sm">
                <img src="./assets/images/header.jpeg" alt="User" class="w-10 h-10 rounded-full border-2 border-white object-cover shadow-sm">
                <img src="https://images.unsplash.com/photo-1539701938214-0d9736e1c16b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80" alt="User" class="w-10 h-10 rounded-full border-2 border-white object-cover shadow-sm">
                <img src="https://images.unsplash.com/photo-1507152832244-10d45c7eda57?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80" alt="User" class="w-10 h-10 rounded-full border-2 border-white object-cover shadow-sm">
              </div>
              <div>
                <div class="text-sm font-medium text-gray-800">Trusted by 10,000+</div>
                <div class="text-xs text-gray-500">satisfied customers</div>
              </div>
            </div>
          </div>
          <div class="w-full md:w-1/2 flex justify-center mt-10 md:mt-0">
             <!-- Keep original image -->
             <img src="./assets/images/header.jpeg" alt="Sbdatasub VTU Service" class="relative z-10 rounded-2xl shadow-xl w-full max-w-md object-cover">
             <!-- Optional: add subtle background shapes or elements -->
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-light-blue">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
            Our Premium Services
          </h2>
          <p class="text-gray-600 max-w-2xl mx-auto text-lg">
            Experience seamless digital transactions with our comprehensive
            range of services designed to make your life easier.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Service Card Structure -->
          <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
            <div class="w-14 h-14 bg-light-green rounded-xl flex items-center justify-center mb-5">
              <i data-lucide="phone" class="text-primary-green" width="28" height="28"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3 text-gray-900">Airtime Recharge</h3>
            <p class="text-gray-600">Instant airtime top-up for all major networks with no hidden charges.</p>
          </div>
           <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
            <div class="w-14 h-14 bg-light-green rounded-xl flex items-center justify-center mb-5">
              <i data-lucide="wifi" class="text-primary-green" width="28" height="28"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3 text-gray-900">Data Bundles</h3>
            <p class="text-gray-600">Affordable data plans for all your browsing and streaming needs.</p>
          </div>
           <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
            <div class="w-14 h-14 bg-light-green rounded-xl flex items-center justify-center mb-5">
              <i data-lucide="tv" class="text-primary-green" width="28" height="28"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3 text-gray-900">Cable TV</h3>
            <p class="text-gray-600">Renew your DSTV, GOTV, and Startimes subscriptions effortlessly.</p>
          </div>
           <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
            <div class="w-14 h-14 bg-light-green rounded-xl flex items-center justify-center mb-5">
              <i data-lucide="zap" class="text-primary-green" width="28" height="28"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3 text-gray-900">Electricity Bills</h3>
            <p class="text-gray-600">Pay your electricity bills instantly and get your token immediately.</p>
          </div>
           <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
            <div class="w-14 h-14 bg-light-green rounded-xl flex items-center justify-center mb-5">
              <i data-lucide="credit-card" class="text-primary-green" width="28" height="28"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3 text-gray-900">Wallet</h3>
            <p class="text-gray-600">Create Wallet for secure online payments and subscriptions.</p>
          </div>
           <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
            <div class="w-14 h-14 bg-light-green rounded-xl flex items-center justify-center mb-5">
              <i data-lucide="globe" class="text-primary-green" width="28" height="28"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3 text-gray-900">International Top-ups</h3>
            <p class="text-gray-600">Send airtime and data to friends and family around the world.</p>
          </div>
          <!-- Repeat for other services -->
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex flex-col md:flex-row gap-12 items-center">
           <div class="w-full md:w-1/2 order-2 md:order-1 pr-0 md:pr-12">
            <!-- Content -->
            <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
              Why Choose SHARE SUB?
            </h2>
            <p class="text-gray-600 mb-8 text-lg">
              Our platform is designed with your convenience in mind, offering a
              range of features that make digital transactions seamless and
              stress-free.
            </p>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-8">
              <!-- Feature Item Structure -->
              <div class="flex gap-4 items-start p-3 rounded-md transition-colors duration-200 hover:bg-light-blue">
                <div class="flex-shrink-0 mt-1 text-primary-blue">
                   <i data-lucide="zap" width="24" height="24"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-lg text-gray-900">Lightning Fast</h3>
                  <p class="text-sm text-gray-600">
                    Transactions are processed instantly with immediate delivery of services.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start p-3 rounded-md transition-colors duration-200 hover:bg-light-blue">
                <div class="flex-shrink-0 mt-1 text-primary-blue">
                   <i data-lucide="shield-check" width="24" height="24"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-lg text-gray-900">Secure Payments</h3>
                  <p class="text-sm text-gray-600">
                    Bank-grade security ensures your transactions are always protected.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start p-3 rounded-md transition-colors duration-200 hover:bg-light-blue">
                <div class="flex-shrink-0 mt-1 text-primary-blue">
                   <i data-lucide="clock" width="24" height="24"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-lg text-gray-900">24/7 Availability</h3>
                  <p class="text-sm text-gray-600">
                    Our services are available round the clock, whenever you need them.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start p-3 rounded-md transition-colors duration-200 hover:bg-light-blue">
                <div class="flex-shrink-0 mt-1 text-primary-blue">
                   <i data-lucide="refresh-cw" width="24" height="24"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-lg text-gray-900">Automated Processes</h3>
                  <p class="text-sm text-gray-600">
                    Fully automated system with no manual intervention required.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start p-3 rounded-md transition-colors duration-200 hover:bg-light-blue">
                <div class="flex-shrink-0 mt-1 text-primary-blue">
                   <i data-lucide="percent" width="24" height="24"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-lg text-gray-900">Best Rates</h3>
                  <p class="text-sm text-gray-600">
                    Competitive rates and discounts for bulk purchases and loyal customers.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start p-3 rounded-md transition-colors duration-200 hover:bg-light-blue">
                <div class="flex-shrink-0 mt-1 text-primary-blue">
                   <i data-lucide="headphones" width="24" height="24"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-lg text-gray-900">24/7 Support</h3>
                  <p class="text-sm text-gray-600">
                    Our customer support team is always ready to assist you.
                  </p>
                </div>
              </div>
              <!-- Repeat for other features -->
            </div>
          </div>
           <div class="w-full md:w-1/2 order-1 md:order-2 flex justify-center items-center">
            <!-- Image -->
             <!-- Keep original image -->
            <img src="./assets/images/section1.jpg" alt="SHARE SUB Features" class="relative z-10 rounded-2xl shadow-xl w-full max-w-md object-cover">
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-20 bg-light-blue">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">How It Works</h2>
          <p class="text-gray-600 max-w-2xl mx-auto text-lg">
            Getting started with Share Sub is quick and easy. Follow these simple
            steps to start enjoying seamless transactions.
          </p>
        </div>
        <div class="relative max-w-4xl mx-auto">
          <!-- Connection Line -->
          <div class="hidden md:block absolute top-10 left-0 right-0 h-1 bg-green-200 transform -translate-y-1/2 z-0 rounded-full"></div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10">
            <!-- Step Item Structure -->
            <div class="flex flex-col items-center text-center group transform transition-transform duration-300 hover:scale-105">
              <div class="mb-6 relative">
                <div class="w-20 h-20 bg-primary-blue text-white rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <i data-lucide="user-plus" width="32" height="32"></i>
                </div>
                <div class="absolute top-0 -right-3 w-8 h-8 bg-white border-2 border-primary-blue rounded-full flex items-center justify-center text-primary-blue font-bold text-sm">
                  1
                </div>
              </div>
              <h3 class="text-xl font-semibold mb-2 text-gray-900">Create Account</h3>
              <p class="text-gray-600 text-sm">Sign up free in minutes with basic info.</p>
            </div>
             <div class="flex flex-col items-center text-center group transform transition-transform duration-300 hover:scale-105">
              <div class="mb-6 relative">
                <div class="w-20 h-20 bg-primary-blue text-white rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <i data-lucide="credit-card" width="32" height="32"></i>
                </div>
                <div class="absolute top-0 -right-3 w-8 h-8 bg-white border-2 border-primary-blue rounded-full flex items-center justify-center text-primary-blue font-bold text-sm">
                  2
                </div>
              </div>
              <h3 class="text-xl font-semibold mb-2 text-gray-900">Fund Wallet</h3>
              <p class="text-gray-600 text-sm">Add funds securely via multiple payment methods.</p>
            </div>
             <div class="flex flex-col items-center text-center group transform transition-transform duration-300 hover:scale-105">
              <div class="mb-6 relative">
                <div class="w-20 h-20 bg-primary-blue text-white rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <i data-lucide="zap" width="32" height="32"></i>
                </div>
                <div class="absolute top-0 -right-3 w-8 h-8 bg-white border-2 border-primary-blue rounded-full flex items-center justify-center text-primary-blue font-bold text-sm">
                  3
                </div>
              </div>
              <h3 class="text-xl font-semibold mb-2 text-gray-900">Select Service</h3>
              <p class="text-gray-600 text-sm">Choose your desired service and enter details.</p>
            </div>
             <div class="flex flex-col items-center text-center group transform transition-transform duration-300 hover:scale-105">
              <div class="mb-6 relative">
                <div class="w-20 h-20 bg-primary-blue text-white rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                   <i data-lucide="check-circle" width="32" height="32"></i>
                </div>
                <div class="absolute top-0 -right-3 w-8 h-8 bg-white border-2 border-primary-blue rounded-full flex items-center justify-center text-primary-blue font-bold text-sm">
                  4
                </div>
              </div>
              <h3 class="text-xl font-semibold mb-2 text-gray-900">Instant Delivery</h3>
              <p class="text-gray-600 text-sm">Receive your purchase instantly with confirmation.</p>
            </div>
            <!-- Repeat for other steps -->
          </div>
        </div>
      </div>
    </section>

    <!-- Download App Section -->
    <section id="download-app" class="py-20 bg-gradient-to-r from-light-blue via-white to-light-blue">
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex flex-col md:flex-row items-center gap-12">
          <div class="w-full md:w-1/2 lg:w-2/5 order-2 md:order-1 flex justify-center md:justify-start">
             <!-- Keep original images -->
            <div class="relative flex items-center justify-center space-x-[-4rem]">
                <img src="./assets/images/Heading.png" alt="SHARE SUB Mobile App" class="relative z-10 w-56 md:w-64 h-auto rounded-3xl shadow-xl border-8 border-white">
                <img src="./assets/images/section.jpg" alt="SHARE SUB Mobile App" class="relative z-10 w-56 md:w-64 h-auto rounded-3xl shadow-xl border-8 border-white">
            </div>
          </div>
          <div class="w-full md:w-1/2 lg:w-3/5 order-1 md:order-2 text-center md:text-left">
            <h2 class="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
              Take SHARE SUB With You Everywhere
            </h2>
            <p class="text-gray-600 mb-8 text-lg">
              Download our mobile app to enjoy seamless transactions on the go. Recharge airtime, pay bills, and manage your account from anywhere, anytime.
            </p>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-10">
               <!-- App Feature Item -->
               <div class="flex gap-4 items-start">
                <div class="w-10 h-10 bg-light-green rounded-full flex items-center justify-center flex-shrink-0">
                  <i data-lucide="zap" class="text-primary-green" width="20" height="20"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-gray-900">Faster Transactions</h3>
                  <p class="text-sm text-gray-600">
                    Complete transactions in seconds.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start">
                <div class="w-10 h-10 bg-light-green rounded-full flex items-center justify-center flex-shrink-0">
                  <i data-lucide="bell" class="text-primary-green" width="20" height="20"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-gray-900">Instant Notifications</h3>
                  <p class="text-sm text-gray-600">
                    Get real-time transaction alerts.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start">
                <div class="w-10 h-10 bg-light-green rounded-full flex items-center justify-center flex-shrink-0">
                  <i data-lucide="fingerprint" class="text-primary-green" width="20" height="20"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-gray-900">Biometric Security</h3>
                  <p class="text-sm text-gray-600">
                    Secure login with fingerprint/face ID.
                  </p>
                </div>
              </div>
               <div class="flex gap-4 items-start">
                <div class="w-10 h-10 bg-light-green rounded-full flex items-center justify-center flex-shrink-0">
                  <i data-lucide="history" class="text-primary-green" width="20" height="20"></i>
                </div>
                <div>
                  <h3 class="font-semibold mb-1 text-gray-900">Transaction History</h3>
                  <p class="text-sm text-gray-600">
                    Access your complete history easily.
                  </p>
                </div>
              </div>
              <!-- Repeat for other features -->
            </div>

            <!-- Download Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
              <a href="#" class="flex items-center justify-center gap-3 bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                <i data-lucide="apple" width="24" height="24"></i>
                <div class="text-left">
                  <div class="text-xs">Download on the</div>
                  <div class="text-sm font-semibold">App Store</div>
                </div>
              </a>
              <a href="#" class="flex items-center justify-center gap-3 bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                <i data-lucide="play" width="24" height="24"></i> <!-- Assuming 'play' is the Google Play icon -->
                <div class="text-left">
                  <div class="text-xs">Get it on</div>
                  <div class="text-sm font-semibold">Google Play</div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-light-blue">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
            What Our Users Say
          </h2>
          <p class="text-gray-600 max-w-2xl mx-auto text-lg">
            Hear directly from our users and find out what they
            have to say about their experience with Share Sub.
          </p>
        </div>
        <div class="max-w-4xl mx-auto">
          <div class="relative">
             <!-- Carousel container -->
             <div class="bg-gradient-to-br from-light-green via-white to-white rounded-lg shadow-xl p-8 md:p-10 overflow-hidden border border-green-100">
                <!-- Testimonial content (loaded via JS) -->
                 <div class="flex flex-col md:flex-row gap-8 items-center">
                    <div class="w-full md:w-1/3 flex flex-col items-center">
                       <img id="testimonial-image" src="./assets/images/header.jpeg" alt="Testimonial User" class="w-24 h-24 rounded-full object-cover border-4 border-green-100 mb-4 shadow-md">
                       <h3 id="testimonial-name" class="font-semibold text-lg text-center text-gray-900">Ahmad Sadiq</h3>
                       <p id="testimonial-role" class="text-gray-500 text-sm text-center">Business Owner</p>
                       <div class="flex justify-center mt-2" id="testimonial-rating">
                         <!-- Stars loaded via JS -->
                       </div>
                    </div>
                    <div class="w-full md:w-2/3">
                       <i data-lucide="quote" class="text-green-200 w-10 h-10 mb-4"></i>
                       <p id="testimonial-text" class="text-lg italic text-gray-700 mb-6">
                         "SHARE SUB has revolutionized how I handle utility payments for my business..."
                       </p>
                    </div>
                 </div>
              </div>
              <!-- Navigation buttons -->
              <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-4 md:-translate-x-12">
                  <button id="prev-testimonial" class="w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-primary-blue hover:bg-gray-100 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-blue focus:ring-offset-2">
                    <i data-lucide="chevron-left" width="24" height="24"></i>
                  </button>
              </div>
              <div class="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-4 md:translate-x-12">
                  <button id="next-testimonial" class="w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-primary-blue hover:bg-gray-100 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-blue focus:ring-offset-2">
                    <i data-lucide="chevron-right" width="24" height="24"></i>
                  </button>
              </div>
              <!-- Dots -->
              <div class="flex justify-center mt-8" id="testimonial-dots">
                 <!-- Dots loaded via JS -->
              </div>
          </div>
        </div>
      </div>
    </section>


    <!-- FAQ Section -->
    <section id="faq" class="py-20 bg-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
            Frequently Asked Questions
          </h2>
          <p class="text-gray-600 max-w-2xl mx-auto text-lg">
            Find answers to common questions about Share Sub's services and how
            to get the most out of our platform.
          </p>
        </div>
        <div class="max-w-3xl mx-auto">
          <div class="border-t border-gray-200 divide-y divide-gray-200">
            <!-- FAQ items loaded via JS -->
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-tr from-primary-blue to-primary-green text-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="max-w-4xl mx-auto text-center">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            Ready to Get Started?
          </h2>
          <p class="text-lg text-gray-200 mb-10 max-w-xl mx-auto">
            Join thousands of satisfied customers who trust Share Sub for their
            digital payment needs. Sign up today!
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/web/login/" class="px-8 py-4 bg-white text-primary-blue hover:bg-gray-100 font-medium rounded-full transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 flex items-center justify-center">
              Get Started Now
              <i data-lucide="arrow-right" class="ml-2" width="18" height="18"></i>
            </a>
             <a href="#faq" class="px-8 py-4 border-2 border-white text-white hover:bg-white/20 font-medium rounded-full transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 flex items-center justify-center">
              View FAQs
              <i data-lucide="help-circle" class="ml-2" width="18" height="18"></i>
            </a>
          </div>
           <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div class="text-4xl font-bold">10K+</div>
              <div class="text-gray-300 mt-1">Active Users</div>
            </div>
            <div>
              <div class="text-4xl font-bold">99.9%</div>
              <div class="text-gray-300 mt-1">Uptime</div>
            </div>
            <div>
              <div class="text-4xl font-bold">1M+</div>
              <div class="text-gray-300 mt-1">Transactions</div>
            </div>
            <div>
              <div class="text-4xl font-bold">4.9/5</div>
              <div class="text-gray-300 mt-1">Rating</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-900 text-gray-300 pt-16 pb-8">
    <div class="container mx-auto px-4 md:px-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
        <!-- Column 1: Logo and Description -->
        <div>
           <a href="#" class="block mb-4">
             <img src="./assets/img/logosharesub.png" alt="SHARE SUB Logo" class="h-9 filter brightness-0 invert"> <!-- White version for dark footer -->
             <!-- If you upload a specific white version: <img src="./assets/img/logo-horizontal-white.png" alt="SHARE SUB Logo" class="h-9"> -->
           </a>
           <p class="text-gray-400 text-sm mb-6">
             Your trusted partner for all digital transactions. Fast, secure, and reliable virtual top-up services.
           </p>
           <div class="flex space-x-4">
             <a href="#" class="text-gray-400 hover:text-white transition-colors">
               <i data-lucide="facebook" width="20" height="20"></i>
             </a>
             <a href="#" class="text-gray-400 hover:text-white transition-colors">
               <i data-lucide="twitter" width="20" height="20"></i>
             </a>
             <a href="#" class="text-gray-400 hover:text-white transition-colors">
               <i data-lucide="instagram" width="20" height="20"></i>
             </a>
             <a href="#" class="text-gray-400 hover:text-white transition-colors">
               <i data-lucide="linkedin" width="20" height="20"></i>
             </a>
           </div>
         </div>
        <!-- Column 2: Quick Links -->
        <div>
          <h3 class="text-lg font-semibold text-white mb-4">Quick Links</h3>
          <ul class="space-y-3">
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Home</a></li>
            <li><a href="#services" class="text-gray-400 hover:text-white transition-colors text-sm">Services</a></li>
            <li><a href="#features" class="text-gray-400 hover:text-white transition-colors text-sm">Features</a></li>
            <li><a href="#how-it-works" class="text-gray-400 hover:text-white transition-colors text-sm">How It Works</a></li>
            <li><a href="#testimonials" class="text-gray-400 hover:text-white transition-colors text-sm">Testimonials</a></li>
            <li><a href="#faq" class="text-gray-400 hover:text-white transition-colors text-sm">FAQ</a></li>
          </ul>
        </div>
        <!-- Column 3: Services -->
        <div>
          <h3 class="text-lg font-semibold text-white mb-4">Services</h3>
          <ul class="space-y-3">
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Airtime Recharge</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Data Bundles</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Cable TV</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Electricity Bills</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Wallet Top-up</a></li>
          </ul>
        </div>
        <!-- Column 4: Contact Info -->
        <div>
          <h3 class="text-lg font-semibold text-white mb-4">Contact Us</h3>
          <ul class="space-y-4">
            <li class="flex items-start">
              <i data-lucide="map-pin" class="text-gray-400 mr-3 flex-shrink-0 mt-1" width="18" height="18"></i>
              <span class="text-gray-400 text-sm">BOMO STREET NEW JOS ROAD NAGOYI ZARIA</span>
            </li>
            <li class="flex items-center">
              <i data-lucide="phone" class="text-gray-400 mr-3 flex-shrink-0" width="18" height="18"></i>
              <span class="text-gray-400 text-sm">07026591356</span> <!-- Updated phone number -->
            </li>
            <li class="flex items-center">
              <i data-lucide="mail" class="text-gray-400 mr-3 flex-shrink-0" width="18" height="18"></i>
              <span class="text-gray-400 text-sm"><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>
      <!-- Footer Bottom -->
      <div class="border-t border-gray-700 pt-8 mt-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="text-gray-500 text-sm mb-4 md:mb-0">
            &copy; <span id="current-year"></span> SHARE SUB. All rights reserved.
          </div>
          <div class="flex space-x-6">
            <a href="/privacy.html" class="text-gray-500 hover:text-white transition-colors text-sm">Privacy Policy</a>
            <a href="/terms.html" class="text-gray-500 hover:text-white transition-colors text-sm">Terms of Service</a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Initialize Lucide icons
    lucide.createIcons();

    // Current year for footer
    document.getElementById('current-year').textContent = new Date().getFullYear();

    // Header scroll effect
    window.addEventListener('scroll', function() {
      const header = document.getElementById('header');
      if (window.scrollY > 10) {
        header.classList.remove('bg-transparent', 'py-4');
        header.classList.add('glass-effect', 'shadow-md', 'py-3', 'border-b', 'border-gray-200'); // Added border
      } else {
        header.classList.add('bg-transparent', 'py-4');
        header.classList.remove('glass-effect', 'shadow-md', 'py-3', 'border-b', 'border-gray-200'); // Removed border
      }
    });

    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = mobileMenuButton.querySelector('[data-lucide="menu"]');
    const closeIcon = mobileMenuButton.querySelector('[data-lucide="x"]');

    mobileMenuButton.addEventListener('click', function() {
      const isHidden = mobileMenu.classList.contains('hidden');
      mobileMenu.classList.toggle('hidden');
      menuIcon.classList.toggle('hidden', !isHidden);
      closeIcon.classList.toggle('hidden', isHidden);
      // Toggle glass effect on mobile menu show/hide if header has it
       if (!isHidden && document.getElementById('header').classList.contains('glass-effect')) {
           mobileMenu.classList.add('glass-effect');
       } else {
           mobileMenu.classList.remove('glass-effect');
       }
    });

    // Close mobile menu when clicking on a link
    const mobileMenuLinks = mobileMenu.querySelectorAll('a');
    mobileMenuLinks.forEach(link => {
      link.addEventListener('click', function() {
        mobileMenu.classList.add('hidden');
        menuIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
         mobileMenu.classList.remove('glass-effect');
      });
    });

    // Testimonials Data (Keep original images)
    const testimonials = [
      {
        name: 'Ahmad Sadiq',
        role: 'Business Owner',
        image: './assets/images/header.jpeg', // Original Image
        text: 'Share Sub has revolutionized how I handle utility payments... saved me countless hours.',
        rating: 5
      },
      {
        name: 'Michael Okafor',
        role: 'Software Developer',
        image: 'https://images.unsplash.com/photo-1539701938214-0d9736e1c16b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80', // Original Image
        text: "Share Sub stands out with its intuitive interface and lightning-fast transactions...",
        rating: 5
      },
      {
        name: 'Amina Bello',
        role: 'Student',
        image: 'https://images.unsplash.com/photo-1589156280159-27698a70f29e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=772&q=80', // Original Image
        text: 'As a student, I appreciate the affordable data bundles... The platform has never let me down.',
        rating: 4
      },
       {
        name: 'David Adeyemi',
        role: 'Digital Marketer',
        image: 'https://images.unsplash.com/photo-1506634572416-48cdfe530110?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80', // Original Image
        text: "The virtual card feature is a game-changer... SHARE SUB offers the best rates.",
        rating: 5
      }
    ];

    // Testimonial Carousel Logic
    let currentTestimonialIndex = 0;
    const testimonialImage = document.getElementById('testimonial-image');
    const testimonialName = document.getElementById('testimonial-name');
    const testimonialRole = document.getElementById('testimonial-role');
    const testimonialText = document.getElementById('testimonial-text');
    const testimonialRating = document.getElementById('testimonial-rating');
    const testimonialDotsContainer = document.getElementById('testimonial-dots');
    const prevTestimonialBtn = document.getElementById('prev-testimonial');
    const nextTestimonialBtn = document.getElementById('next-testimonial');

    function updateTestimonial(index) {
      if (!testimonials || testimonials.length === 0) return;
      const testimonial = testimonials[index];
      testimonialImage.src = testimonial.image;
      testimonialImage.alt = testimonial.name;
      testimonialName.textContent = testimonial.name;
      testimonialRole.textContent = testimonial.role;
      testimonialText.textContent = `"${testimonial.text}"`;

      // Update rating stars
      testimonialRating.innerHTML = '';
      for (let i = 0; i < 5; i++) {
        const star = document.createElement('i');
        star.setAttribute('data-lucide', 'star');
        star.classList.add('w-4', 'h-4'); // Size adjustment
        star.classList.toggle('text-yellow-400', i < testimonial.rating);
        star.classList.toggle('fill-yellow-400', i < testimonial.rating);
        star.classList.toggle('text-gray-300', i >= testimonial.rating);
        testimonialRating.appendChild(star);
      }

       // Update dots
       const dots = testimonialDotsContainer.querySelectorAll('button');
       dots.forEach((dot, i) => {
           dot.classList.toggle('bg-primary-blue', i === index);
           dot.classList.toggle('w-3', i === index); // Make active dot bigger
           dot.classList.toggle('h-3', i === index);
           dot.classList.toggle('bg-gray-300', i !== index);
           dot.classList.toggle('w-2', i !== index);
           dot.classList.toggle('h-2', i !== index);

       });

       currentTestimonialIndex = index;
       lucide.createIcons(); // Re-render icons if needed
    }

    // Create dots
     if (testimonialDotsContainer) {
        testimonialDotsContainer.innerHTML = ''; // Clear existing dots
        testimonials.forEach((_, index) => {
          const dot = document.createElement('button');
          dot.className = `rounded-full mx-1 transition-all duration-300 ${index === 0 ? 'bg-primary-blue w-3 h-3' : 'bg-gray-300 w-2 h-2'}`;
          dot.addEventListener('click', () => updateTestimonial(index));
          testimonialDotsContainer.appendChild(dot);
        });
     }


    // Event Listeners for buttons
    if(prevTestimonialBtn && nextTestimonialBtn){
        prevTestimonialBtn.addEventListener('click', () => {
          const newIndex = currentTestimonialIndex === 0 ? testimonials.length - 1 : currentTestimonialIndex - 1;
          updateTestimonial(newIndex);
        });

        nextTestimonialBtn.addEventListener('click', () => {
          const newIndex = currentTestimonialIndex === testimonials.length - 1 ? 0 : currentTestimonialIndex + 1;
          updateTestimonial(newIndex);
        });
        // Auto-play (optional)
        // setInterval(() => {
        //   const newIndex = currentTestimonialIndex === testimonials.length - 1 ? 0 : currentTestimonialIndex + 1;
        //   updateTestimonial(newIndex);
        // }, 5000); // Change slide every 5 seconds

        // Initialize first testimonial
        updateTestimonial(0);
    }


    // FAQ Data
    const faqs = [
       {
        question: 'How do I create an account on Share Sub?',
        answer: "Creating an account is simple... Click 'Sign Up'..."
      },
      {
        question: 'What payment methods are accepted?',
        answer: 'We accept various methods including cards, bank transfer, USSD...'
      },
      {
        question: 'How long does it take to process transactions?',
        answer: 'Most transactions are instant. Rarely, network issues might cause slight delays.'
      },
       {
        question: 'Is my payment information secure?',
        answer: 'Yes, we use bank-grade security and encryption to protect your data.'
      }
      // Add more FAQs as needed
    ];

    // FAQ Accordion Logic
    const faqContainer = document.querySelector('#faq .divide-y');
    if (faqContainer) {
      faqContainer.innerHTML = ''; // Clear existing content
      faqs.forEach((faq) => {
        const faqItem = document.createElement('div');
        faqItem.className = 'py-5';

        const faqButton = document.createElement('button');
        faqButton.className = 'flex justify-between items-center w-full text-left group focus:outline-none p-1 rounded-md hover:bg-light-blue focus:bg-light-blue transition-colors duration-200';
        faqButton.innerHTML = `
          <span class="font-medium text-lg text-gray-800 group-hover:text-primary-blue group-focus:text-primary-blue">${faq.question}</span>
          <span class="text-primary-blue transform transition-transform duration-300 group-[.open]:rotate-180">
             <i data-lucide="chevron-down" width="20" height="20"></i>
          </span>
        `;

        const faqContent = document.createElement('div');
        faqContent.className = 'transition-all duration-300 ease-in-out overflow-hidden max-h-0 mt-2';
        faqContent.innerHTML = `<p class="text-gray-600 pt-2 pl-1 border-l-2 border-green-100 ml-1">${faq.answer}</p>`;

        faqItem.appendChild(faqButton);
        faqItem.appendChild(faqContent);
        faqContainer.appendChild(faqItem);

        faqButton.addEventListener('click', () => {
          const iconWrapper = faqButton.querySelector('span:last-child');
          const icon = iconWrapper.querySelector('i');

          if (faqContent.classList.contains('max-h-96')) {
            faqContent.classList.remove('max-h-96', 'mt-2', 'pt-2', 'border-l-2', 'border-green-100');
            faqContent.classList.add('max-h-0');
            // iconWrapper.classList.remove('rotate-180'); // JS now handles rotation via group state
             faqButton.classList.remove('open'); // Remove open state
          } else {
            // Close other open FAQs first (optional)
            // faqContainer.querySelectorAll('.max-h-96').forEach(openContent => {
            //   openContent.classList.remove('max-h-96');
            //   openContent.classList.add('max-h-0');
            //   openContent.previousElementSibling.querySelector('span:last-child').classList.remove('rotate-180');
            // });

            faqContent.classList.remove('max-h-0');
            faqContent.classList.add('max-h-96', 'mt-2', 'pt-2', 'border-l-2', 'border-green-100'); // Use max-h utility & add padding/border when open
            // iconWrapper.classList.add('rotate-180'); // JS now handles rotation via group state
             faqButton.classList.add('open'); // Add open state for icon rotation
          }
           lucide.createIcons(); // Re-render Lucide icons if needed
        });
      });
    }

    // Final Lucide icon initialization after dynamic content
    lucide.createIcons();
  </script>
</body>
</html>