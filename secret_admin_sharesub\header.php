<?php
include "../config.php";
if (!isset($_SESSION['email'])) {
  header('location:login');
}
if (!isset($_SESSION['LAST_ACTIVITY'])) {
  header('location:login');
}
if (!isset($_SESSION['admin']) || $_SESSION['admin'] != 'Admins') {
  session_start();
  session_destroy();
  session_unset();
  header('location:../web/login');
}
if (time() - $_SESSION['LAST_ACTIVITY'] > 6000) {
  session_start();
  session_destroy();
  session_unset();
  header("location:login");
}
$_SESSION['LAST_ACTIVITY'] = time();
function parseAmt($amt)
{
  $val = intval($amt);
  if ($val > 999999) {
    return substr_replace(substr_replace($val, ',', -3, 0), ',', -7, 0);
  } elseif ($val > 999) {
    return substr_replace($val, ',', -3, 0);
  } else {
    return $val;
  }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>Admin </title>
  <!-- plugins:css -->
  <link rel="stylesheet" href="vendors/feather/feather.css">
  <link rel="stylesheet" href="vendors/mdi/css/materialdesignicons.min.css">
  <link rel="stylesheet" href="vendors/ti-icons/css/themify-icons.css">
  <link rel="stylesheet" href="vendors/typicons/typicons.css">
  <link rel="stylesheet" href="vendors/simple-line-icons/css/simple-line-icons.css">
  <link rel="stylesheet" href="vendors/css/vendor.bundle.base.css">
  <!-- endinject -->
  <!-- Plugin css for this page -->
  <link rel="stylesheet" href="vendors/datatables.net-bs4/dataTables.bootstrap4.css">
  <link rel="stylesheet" href="js/select.dataTables.min.css">
  <!-- End plugin css for this page -->
  <!-- inject:css -->
  <link rel="stylesheet" href="css/vertical-layout-light/style.css">
  <!-- endinject -->
  <link rel="shortcut icon" href="../favicon.png" />
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/slick-loader@1.1.20/slick-loader.min.css">
  <script src="https://unpkg.com/slick-loader@1.1.20/slick-loader.min.js"></script>
  <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/@mdi/font@6.9.96/css/materialdesignicons.min.css">
  
  <!-- Responsive CSS -->
  <link rel="stylesheet" href="css/responsive.css">
  
  <!-- Custom responsive CSS -->
  <style>
    @media (max-width: 991px) {
      .navbar .navbar-brand-wrapper {
        width: 55px !important;
      }
      .navbar .navbar-menu-wrapper {
        width: calc(100% - 55px) !important;
      }
      .navbar {
        z-index: 1001 !important; /* Higher than sidebar */
      }
      .sidebar {
        z-index: 1000;
        position: fixed !important;
        top: 70px !important;
        left: -300px !important;
        height: calc(100vh - 70px) !important;
        width: 260px !important;
        transition: left 0.3s ease !important;
        box-shadow: none !important;
        background: white !important;
      }
      .sidebar.active {
        left: 0 !important;
        box-shadow: 2px 0 10px rgba(0,0,0,0.2) !important;
      }
      .welcome-text {
        font-size: 1.2rem !important;
      }
      .welcome-sub-text {
        font-size: 0.9rem !important;
      }
      .sidebar .nav .nav-item .menu-title {
        font-size: 0.9rem;
      }
      .sidebar .nav {
        padding-left: 0;
        padding-right: 0;
      }
      .navbar-menu-wrapper {
        padding-left: 10px !important;
        padding-right: 10px !important;
      }
    }
  </style>
</head>

<body>
  <div class="container-scroller">

    <!-- partial:partials/_navbar -->
    <nav class="navbar default-layout col-lg-12 col-12 p-0 fixed-top d-flex align-items-top flex-row">
      <div class="text-center navbar-brand-wrapper d-flex align-items-center justify-content-start">
        <div class="me-3">
          <button class="navbar-toggler navbar-toggler align-self-center" type="button" data-bs-toggle="minimize">
            <span class="icon-menu"></span>
          </button>
        </div>
        <div>
          <a class="navbar-brand brand-logo" href="index">
            <img src="images/logo.png" alt="<?= $config['site_name'] ?> Logo" style="max-height: 40px;" />
          </a>
          <a class="navbar-brand brand-logo-mini" href="index">
            <img src="images/logo.png" alt="<?= $config['site_name'] ?> Logo" style="max-height: 30px;" />
          </a>
        </div>
      </div>
      <div class="navbar-menu-wrapper d-flex align-items-top">
        <ul class="navbar-nav">
          <li class="nav-item font-weight-semibold d-none d-lg-block ms-0">
            <h1 class="welcome-text"> <span id="greeting"></span>, <span class="text-black fw-bold">Admin</span></h1>
            <h3 class="welcome-sub-text">Your customers' performance summary is here </h3>
          </li>
        </ul>
        <ul class="navbar-nav ms-auto" style="visibility: hidden;">
          <li class="nav-item dropdown d-none d-lg-block">
            <a class="nav-link dropdown-bordered dropdown-toggle dropdown-toggle-split" id="messageDropdown" href="#" data-bs-toggle="dropdown" aria-expanded="false"> Select Category </a>
            <div class="dropdown-menu dropdown-menu-right navbar-dropdown preview-list pb-0" aria-labelledby="messageDropdown">
              <a class="dropdown-item py-3">
                <p class="mb-0 font-weight-medium float-left">Select category</p>
              </a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item preview-item">
                <div class="preview-item-content flex-grow py-2">
                  <p class="preview-subject ellipsis font-weight-medium text-dark">Bootstrap Bundle </p>
                  <p class="fw-light small-text mb-0">This is a Bundle featuring 16 unique dashboards</p>
                </div>
              </a>
              <a class="dropdown-item preview-item">
                <div class="preview-item-content flex-grow py-2">
                  <p class="preview-subject ellipsis font-weight-medium text-dark">Angular Bundle</p>
                  <p class="fw-light small-text mb-0">Everything you'll ever need for your Angular projects</p>
                </div>
              </a>
              <a class="dropdown-item preview-item">
                <div class="preview-item-content flex-grow py-2">
                  <p class="preview-subject ellipsis font-weight-medium text-dark">VUE Bundle</p>
                  <p class="fw-light small-text mb-0">Bundle of 6 Premium Vue Admin Dashboard</p>
                </div>
              </a>
              <a class="dropdown-item preview-item">
                <div class="preview-item-content flex-grow py-2">
                  <p class="preview-subject ellipsis font-weight-medium text-dark">React Bundle</p>
                  <p class="fw-light small-text mb-0">Bundle of 8 Premium React Admin Dashboard</p>
                </div>
              </a>
            </div>
          </li>
          <li class="nav-item d-none d-lg-block">
            <div id="datepicker-popup" class="input-group date datepicker navbar-date-picker">
              <span class="input-group-addon input-group-prepend border-right">
                <span class="icon-calendar input-group-text calendar-icon"></span>
              </span>
              <input type="text" class="form-control">
            </div>
          </li>
          <li class="nav-item">
            <form class="search-form" action="#">
              <i class="icon-search"></i>
              <input type="search" class="form-control" placeholder="Search Here" title="Search here">
            </form>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link count-indicator" id="notificationDropdown" href="#" data-bs-toggle="dropdown">
              <i class="icon-mail icon-lg"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right navbar-dropdown preview-list pb-0" aria-labelledby="notificationDropdown">
              <a class="dropdown-item py-3 border-bottom">
                <p class="mb-0 font-weight-medium float-left">You have 4 new notifications </p>
                <span class="badge badge-pill badge-primary float-right">View all</span>
              </a>
              <a class="dropdown-item preview-item py-3">
                <div class="preview-thumbnail">
                  <i class="mdi mdi-alert m-auto text-primary"></i>
                </div>
                <div class="preview-item-content">
                  <h6 class="preview-subject fw-normal text-dark mb-1">Application Error</h6>
                  <p class="fw-light small-text mb-0"> Just now </p>
                </div>
              </a>
              <a class="dropdown-item preview-item py-3">
                <div class="preview-thumbnail">
                  <i class="mdi mdi-settings m-auto text-primary"></i>
                </div>
                <div class="preview-item-content">
                  <h6 class="preview-subject fw-normal text-dark mb-1">Settings</h6>
                  <p class="fw-light small-text mb-0"> Private message </p>
                </div>
              </a>
              <a class="dropdown-item preview-item py-3">
                <div class="preview-thumbnail">
                  <i class="mdi mdi-airballoon m-auto text-primary"></i>
                </div>
                <div class="preview-item-content">
                  <h6 class="preview-subject fw-normal text-dark mb-1">New user registration</h6>
                  <p class="fw-light small-text mb-0"> 2 days ago </p>
                </div>
              </a>
            </div>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link count-indicator" id="countDropdown" href="#" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="icon-bell"></i>
              <span class="count"></span>
            </a>
            <div class="dropdown-menu dropdown-menu-right navbar-dropdown preview-list pb-0" aria-labelledby="countDropdown">
              <a class="dropdown-item py-3">
                <p class="mb-0 font-weight-medium float-left">You have 7 unread mails </p>
                <span class="badge badge-pill badge-primary float-right">View all</span>
              </a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item preview-item">
                <div class="preview-thumbnail">
                  <img src="images/faces/face10.jpg" alt="image" class="img-sm profile-pic">
                </div>
                <div class="preview-item-content flex-grow py-2">
                  <p class="preview-subject ellipsis font-weight-medium text-dark">Marian Garner </p>
                  <p class="fw-light small-text mb-0"> The meeting is cancelled </p>
                </div>
              </a>
              <a class="dropdown-item preview-item">
                <div class="preview-thumbnail">
                  <img src="images/faces/face12.jpg" alt="image" class="img-sm profile-pic">
                </div>
                <div class="preview-item-content flex-grow py-2">
                  <p class="preview-subject ellipsis font-weight-medium text-dark">David Grey </p>
                  <p class="fw-light small-text mb-0"> The meeting is cancelled </p>
                </div>
              </a>
              <a class="dropdown-item preview-item">
                <div class="preview-thumbnail">
                  <img src="images/faces/face1.jpg" alt="image" class="img-sm profile-pic">
                </div>
                <div class="preview-item-content flex-grow py-2">
                  <p class="preview-subject ellipsis font-weight-medium text-dark">Travis Jenkins </p>
                  <p class="fw-light small-text mb-0"> The meeting is cancelled </p>
                </div>
              </a>
            </div>
          </li>
          <li class="nav-item dropdown d-none d-lg-block user-dropdown">
            <a class="nav-link" id="UserDropdown" href="#" data-bs-toggle="dropdown" aria-expanded="false">
              <img class="img-xs rounded-circle" src="images/faces/face8.jpg" alt="Profile image"> </a>
            <div class="dropdown-menu dropdown-menu-right navbar-dropdown" aria-labelledby="UserDropdown">
              <div class="dropdown-header text-center">
                <img class="img-md rounded-circle" src="images/faces/face8.jpg" alt="Profile image">
                <p class="mb-1 mt-3 font-weight-semibold">Allen Moreno</p>
                <p class="fw-light text-muted mb-0"><EMAIL></p>
              </div>
              <a class="dropdown-item"><i class="dropdown-item-icon mdi mdi-account-outline text-primary me-2"></i> My Profile <span class="badge badge-pill badge-danger">1</span></a>
              <a class="dropdown-item"><i class="dropdown-item-icon mdi mdi-message-text-outline text-primary me-2"></i> Messages</a>
              <a class="dropdown-item"><i class="dropdown-item-icon mdi mdi-calendar-check-outline text-primary me-2"></i> Activity</a>
              <a class="dropdown-item"><i class="dropdown-item-icon mdi mdi-help-circle-outline text-primary me-2"></i> FAQ</a>
              <a class="dropdown-item"><i class="dropdown-item-icon mdi mdi-power text-primary me-2"></i>Sign Out</a>
            </div>
          </li>
        </ul>
        <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
          <span class="mdi mdi-menu"></span>
        </button>
      </div>
    </nav>
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">
      <!-- partial:partials/_settings-panel -->


      <!-- partial -->
      <!-- partial:partials/_sidebar -->
      <nav class="sidebar sidebar-offcanvas" id="sidebar">
        <ul class="nav">
          <li class="nav-item">
            <a class="nav-link" href="index">
              <i class="mdi mdi-grid-large menu-icon"></i>
              <span class="menu-title">Dashboard</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="transactions">
              <i class="menu-icon mdi mdi-file-document-box"></i>
              <span class="menu-title">Transactions</span>
            </a>
          </li>
          <?php if ((isset($_SESSION['role']) and $_SESSION['role'] == 'main')) : ?>
            <li class="nav-item">
              <a class="nav-link" href="api_transactions">
                <i class="menu-icon mdi mdi-file-document-box"></i>
                <span class="menu-title">Transactions (Details)</span>
              </a>
            </li>
          <?php endif ?>

          <li class="nav-item">
            <a class="nav-link" href="users">
              <i class="menu-icon mdi mdi-account-star"></i>
              <span class="menu-title">All Users</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="notifications">
              <i class="menu-icon mdi mdi-bell-ring"></i>
              <span class="menu-title">Notifications</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="user">
              <i class="menu-icon mdi mdi-account"></i>
              <span class="menu-title">User Profile</span>
            </a>
          </li>
          <?php if ((isset($_SESSION['role']) && $_SESSION['role'] == 'main')) : ?>
            <li class="nav-item">
              <a class="nav-link" href="user_migration">
                <i class="menu-icon mdi mdi-swap-vertical"></i>
                <span class="menu-title">User Migration</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="manual_funding">
                <i class="menu-icon mdi mdi-comment-account"></i>
                <span class="menu-title">Fund User</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="update_apikeys">
                <i class="menu-icon mdi mdi-airballoon"></i>
                <span class="menu-title">API Credential</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="credentials">
                <i class="menu-icon mdi mdi-layers-outline"></i>
                <span class="menu-title">Admin Credential</span>
              </a>
            </li>
          <?php endif ?>



          <li class="nav-item">
            <a class="nav-link" href="statistic">
              <i class="menu-icon mdi mdi mdi-chart-bubble"></i>
              <span class="menu-title">Statistic</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="extra">
              <i class="menu-icon mdi mdi mdi-apps"></i>
              <span class="menu-title">Extra</span>
            </a>
          </li>
          <?php if ((isset($_SESSION['role']) and $_SESSION['role'] == 'main')) : ?>
            <li class="nav-item">
              <a class="nav-link" href="upgrade_plan">
                <i class="menu-icon mdi mdi mdi-apps"></i>
                <span class="menu-title">UPGRADING COST</span>
              </a>
            </li>
          <?php endif ?>
          <li class="nav-item">
            <a class="nav-link" href="ref_cost">
              <i class="menu-icon mdi mdi mdi-apps"></i>
              <span class="menu-title">REFERRAL BONUS</span>
            </a>
          </li>
          <?php if ((isset($_SESSION['role']) and $_SESSION['role'] == 'main')) : ?>
            <li class="nav-item nav-category">DATA</li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="collapse" href="#ui-data" aria-expanded="false" aria-controls="ui-data">
                <i class="menu-icon mdi mdi-floor-plan"></i>
                <span class="menu-title">PLANS</span>
                <i class="menu-arrow"></i>
              </a>
              <div class="collapse" id="ui-data">
                <ul class="nav flex-column sub-menu">

                  <li class="nav-item"> <a class="nav-link" href="data_plans">View Plans</a></li>


                  <li class="nav-item"> <a class="nav-link" href="add_plan">Add Plans</a></li>
                  <li class="nav-item"> <a class="nav-link" href="data_card">View Data Card</a></li>
                  <li class="nav-item"> <a class="nav-link" href="add_data_card">Add Data Card</a></li>
                  <li class="nav-item"> <a class="nav-link" href="type_switches">Data Switches</a></li>
                  <li class="nav-item"> <a class="nav-link" href="Update_ad">Advertisment</a></li>
                  <!-- <li class="nav-item"> <a class="nav-link" href="view_platform">View Platforms</a></li> -->
                </ul>
              </div>
            </li>
            <li class="nav-item nav-category">AIRTIME</li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="collapse" href="#ui-air" aria-expanded="false" aria-controls="ui-air">
                <i class="menu-icon mdi mdi-floor-plan"></i>
                <span class="menu-title">AIRTIME</span>
                <i class="menu-arrow"></i>
              </a>
              <div class="collapse" id="ui-air">
                <ul class="nav flex-column sub-menu">
                  <li class="nav-item"> <a class="nav-link" href="airtime_config">Config</a></li>
                </ul>
              </div>
            </li>

            <li class="nav-item nav-category">ELECTRICITY</li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="collapse" href="#ui-bill" aria-expanded="false" aria-controls="ui-bill">
                <i class="menu-icon mdi mdi-floor-plan"></i>
                <span class="menu-title">ELECTRICITY</span>
                <i class="menu-arrow"></i>
              </a>
              <div class="collapse" id="ui-bill">
                <ul class="nav flex-column sub-menu">

                  <li class="nav-item"> <a class="nav-link" href="bill_config">Config</a></li>
                  <li class="nav-item"> <a class="nav-link" href="add_bill">Add Bill Plan</a></li>
                  <li class="nav-item"> <a class="nav-link" href="bill_plans">View Bill Plan</a></li>

                </ul>
              </div>
            </li>

            <li class="nav-item nav-category">CABLE</li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="collapse" href="#ui-cable" aria-expanded="false" aria-controls="ui-cable">
                <i class="menu-icon mdi mdi-floor-plan"></i>
                <span class="menu-title">CABLE</span>
                <i class="menu-arrow"></i>
              </a>
              <div class="collapse" id="ui-cable">
                <ul class="nav flex-column sub-menu">
                  <!-- <li class="nav-item"> <a class="nav-link" href="cable_discount">Discount</a></li> -->
                  <li class="nav-item"> <a class="nav-link" href="cable_config">Config</a></li>
                  <li class="nav-item"> <a class="nav-link" href="add_cable">Add Cable Plan</a></li>
                  <li class="nav-item"> <a class="nav-link" href="cable_plans">View Cable Plan</a></li>
                </ul>
              </div>
            </li>
          <?php endif ?>
          <li class="nav-item nav-category">KYC</li>
          <li class="nav-item">
            <a class="nav-link" data-bs-toggle="collapse" href="#ui-kyc" aria-expanded="false" aria-controls="ui-kyc">
              <i class="menu-icon mdi mdi-floor-plan"></i>
              <span class="menu-title">KYC</span>
              <i class="menu-arrow"></i>
            </a>
            <div class="collapse" id="ui-kyc">
              <ul class="nav flex-column sub-menu">
                <li class="nav-item"> <a class="nav-link" href="kyc_users">Validated Users</a></li>
                <li class="nav-item"> <a class="nav-link" href="spent">Max Day Spent</a></li>
                <!-- <li class="nav-item"> <a class="nav-link" href="type_switches">Type Switches</a></li> -->
              </ul>
            </div>
          </li>
          <li class="nav-item nav-category">A2C</li>
          <li class="nav-item">
            <a class="nav-link" data-bs-toggle="collapse" href="#ui-bill" aria-expanded="false" aria-controls="ui-bill">
              <i class="menu-icon mdi mdi-floor-plan"></i>
              <span class="menu-title">Airtime2Cash</span>
              <i class="menu-arrow"></i>
            </a>
            <div class="collapse" id="ui-bill">
              <ul class="nav flex-column sub-menu">

                <li class="nav-item"> <a class="nav-link" href="a2c_config">Config</a></li>
                <li class="nav-item"> <a class="nav-link" href="a2c_transactions">A2C Transactions</a></li>

              </ul>
            </div>
          </li>
          <li class="nav-item nav-category">EXAM</li>
          <li class="nav-item">
            <a class="nav-link" data-bs-toggle="collapse" href="#exam" aria-expanded="false" aria-controls="ui-rc">
              <i class="menu-icon mdi mdi-floor-plan"></i>
              <span class="menu-title">EXAM</span>
              <i class="menu-arrow"></i>
            </a>

            <div class="collapse" id="exam">
              <ul class="nav flex-column sub-menu">
                <li class="nav-item"> <a class="nav-link" href="exam_add">Add Exam Card</a></li>
                <li class="nav-item"> <a class="nav-link" href="exam">Config</a></li>
              </ul>
            </div>
          </li>
          <li class="nav-item nav-category">RECHARGE CARD</li>
          <li class="nav-item">
            <a class="nav-link" data-bs-toggle="collapse" href="#ui-rc" aria-expanded="false" aria-controls="ui-rc">
              <i class="menu-icon mdi mdi-floor-plan"></i>
              <span class="menu-title">Recharge Card</span>
              <i class="menu-arrow"></i>
            </a>
            <div class="collapse" id="ui-rc">
              <ul class="nav flex-column sub-menu">
                <li class="nav-item"> <a class="nav-link" href="rc">Config</a></li>
              </ul>
            </div>
          </li>
          <?php if ((isset($_SESSION['role']) && $_SESSION['role'] == 'main')) : ?>
            <li class="nav-item">
              <a class="nav-link" href="user_type_name">
                <i class="menu-icon mdi mdi-account"></i>
                <span class="menu-title">User Type Names</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="faq">
                <i class="menu-icon mdi mdi-apps"></i>
                <span class="menu-title">FAQs</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add_admin">
                <i class="menu-icon mdi mdi-apps"></i>
                <span class="menu-title">Add Admin</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="upgrade_caption">
                <i class="menu-icon mdi mdi-apps"></i>
                <span class="menu-title">Upgrade Caption</span>
              </a>
            </li>
          <?php endif ?>
          <li class="nav-item">
            <a class="nav-link" href="logout">
              <i class="menu-icon mdi mdi-power-socket"></i>
              <span class="menu-title">Logout</span>
            </a>
          </li>
          <!--  -->
        </ul>
      </nav>
      <!-- partial -->
      <div class="main-panel">
        <script type="text/javascript">
          const today = new Date()
          const now = today.getHours()
          const greeting = document.getElementById("greeting")
          const msg = document.getElementById("msg")
          let message = 'Good'
          let msgC
          console.log(now)
          if (now < 12 && now > 5) {
            message = `${message} Morning`;
            msgC = ` 😋`
          } else if (now < 19 && now > 11) {
            message = `${message} Afternoon`
            msgC = `It is working Time! 💻`
          } else {
            message = `${message} Evening`
            msgC = `It is Sleepy time! 😴`
          }
          greeting.innerText = message;
        </script>